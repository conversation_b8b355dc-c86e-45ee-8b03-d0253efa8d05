Analysis started at: 2025-07-31 19:28:31
Log file: block_sparsity_patterns_75_16*16_llama2_8layer/analysis_log_20250731_192831.txt
================================================================================
Loading model: /mnt/sdb/jjji/Pruned_Models/llama2_13b_chat_pruned_models/wanda_75_wikitext2
Detected model type: llama

Model Information:
Number of layers: 40
Hidden dimension: 5120
Number of attention heads: 40

Analysis Parameters:
Block size: 16x16
Sample ratio: 0.5%
Random seed: 42

=== Processing Layer 8 ===
Analyzing layer 8 (model type: llama)

Processing weight: Q_proj
Weight matrix shape: (5120, 5120)
Block size: 16x16
Number of blocks: 320 x 320 = 102400
Sampled blocks: 512
Block sparsity pattern saved to: block_sparsity_patterns_75_16*16_llama2_8layer/layer_8_Q_proj_block_sparsity_16x16.pdf
Sparsity distribution saved to: block_sparsity_patterns_75_16*16_llama2_8layer/layer_8_Q_proj_sparsity_distribution_16x16.pdf
  Sampled blocks: 512
  Average sparsity: 74.86%
  Sparsity range: [63.67%, 86.72%]

Processing weight: K_proj
Weight matrix shape: (5120, 5120)
Block size: 16x16
Number of blocks: 320 x 320 = 102400
Sampled blocks: 512
Block sparsity pattern saved to: block_sparsity_patterns_75_16*16_llama2_8layer/layer_8_K_proj_block_sparsity_16x16.pdf
Sparsity distribution saved to: block_sparsity_patterns_75_16*16_llama2_8layer/layer_8_K_proj_sparsity_distribution_16x16.pdf
  Sampled blocks: 512
  Average sparsity: 74.71%
  Sparsity range: [62.11%, 85.55%]

Processing weight: V_proj
Weight matrix shape: (5120, 5120)
Block size: 16x16
Number of blocks: 320 x 320 = 102400
Sampled blocks: 512
Block sparsity pattern saved to: block_sparsity_patterns_75_16*16_llama2_8layer/layer_8_V_proj_block_sparsity_16x16.pdf
Sparsity distribution saved to: block_sparsity_patterns_75_16*16_llama2_8layer/layer_8_V_proj_sparsity_distribution_16x16.pdf
  Sampled blocks: 512
  Average sparsity: 74.84%
  Sparsity range: [60.16%, 84.38%]

Processing weight: O_proj
Weight matrix shape: (5120, 5120)
Block size: 16x16
Number of blocks: 320 x 320 = 102400
Sampled blocks: 512
Block sparsity pattern saved to: block_sparsity_patterns_75_16*16_llama2_8layer/layer_8_O_proj_block_sparsity_16x16.pdf
Sparsity distribution saved to: block_sparsity_patterns_75_16*16_llama2_8layer/layer_8_O_proj_sparsity_distribution_16x16.pdf
  Sampled blocks: 512
  Average sparsity: 74.40%
  Sparsity range: [15.62%, 100.00%]

Processing weight: up_proj
Weight matrix shape: (13824, 5120)
Block size: 16x16
Number of blocks: 864 x 320 = 276480
Sampled blocks: 1382
Block sparsity pattern saved to: block_sparsity_patterns_75_16*16_llama2_8layer/layer_8_up_proj_block_sparsity_16x16.pdf
Sparsity distribution saved to: block_sparsity_patterns_75_16*16_llama2_8layer/layer_8_up_proj_sparsity_distribution_16x16.pdf
  Sampled blocks: 1382
  Average sparsity: 75.03%
  Sparsity range: [62.50%, 86.72%]

Processing weight: gate_proj
Weight matrix shape: (13824, 5120)
Block size: 16x16
Number of blocks: 864 x 320 = 276480
Sampled blocks: 1382
Block sparsity pattern saved to: block_sparsity_patterns_75_16*16_llama2_8layer/layer_8_gate_proj_block_sparsity_16x16.pdf
Sparsity distribution saved to: block_sparsity_patterns_75_16*16_llama2_8layer/layer_8_gate_proj_sparsity_distribution_16x16.pdf
  Sampled blocks: 1382
  Average sparsity: 75.08%
  Sparsity range: [62.50%, 85.94%]

Processing weight: down_proj
Weight matrix shape: (5120, 13824)
Block size: 16x16
Number of blocks: 320 x 864 = 276480
Sampled blocks: 1382
Block sparsity pattern saved to: block_sparsity_patterns_75_16*16_llama2_8layer/layer_8_down_proj_block_sparsity_16x16.pdf
Sparsity distribution saved to: block_sparsity_patterns_75_16*16_llama2_8layer/layer_8_down_proj_sparsity_distribution_16x16.pdf
  Sampled blocks: 1382
  Average sparsity: 74.96%
  Sparsity range: [52.73%, 93.75%]

Analysis complete! Results saved in: block_sparsity_patterns_75_16*16_llama2_8layer
Analysis finished at: 2025-07-31 19:36:56
