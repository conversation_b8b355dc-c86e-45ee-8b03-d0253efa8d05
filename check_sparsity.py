#!/usr/bin/env python3
"""
简单脚本检查模型的稀疏性
"""

import torch
import numpy as np
from transformers import LlamaForCausalLM, AutoConfig
import argparse

def check_model_sparsity(model_path):
    """检查模型的稀疏性"""
    print(f"正在加载模型: {model_path}")
    
    try:
        # 尝试确定模型类型
        config = AutoConfig.from_pretrained(model_path)
        model_type = config.model_type.lower()
        print(f"检测到模型类型: {model_type}")
        
        if model_type == 'llama':
            model = LlamaForCausalLM.from_pretrained(model_path, torch_dtype=torch.float32)
        else:
            print(f"不支持的模型类型: {model_type}")
            return
            
    except Exception as e:
        print(f"加载模型时出错: {e}")
        return
    
    model.eval()
    
    # 检查第一层的权重
    layer_0 = model.model.layers[0]
    
    print(f"\n=== 第0层权重分析 ===")
    
    # 检查注意力权重
    print("\n注意力权重:")
    for name, proj in [('q_proj', layer_0.self_attn.q_proj), 
                       ('k_proj', layer_0.self_attn.k_proj),
                       ('v_proj', layer_0.self_attn.v_proj),
                       ('o_proj', layer_0.self_attn.o_proj)]:
        weight = proj.weight.data
        zero_count = (weight == 0).sum().item()
        total_count = weight.numel()
        sparsity = zero_count / total_count * 100
        
        print(f"  {name}: 形状={weight.shape}, 零值={zero_count}/{total_count} ({sparsity:.2f}%)")
        print(f"    权重范围: [{weight.min().item():.6f}, {weight.max().item():.6f}]")
        print(f"    权重均值: {weight.mean().item():.6f}, 标准差: {weight.std().item():.6f}")
        
        # 显示一些具体的权重值
        flat_weights = weight.flatten()
        print(f"    前10个权重值: {flat_weights[:10].tolist()}")
        
        # 检查是否有接近零但不完全为零的值
        near_zero = torch.abs(weight) < 1e-6
        near_zero_count = near_zero.sum().item()
        print(f"    接近零的值 (|w| < 1e-6): {near_zero_count}")
        
    # 检查MLP权重
    print("\nMLP权重:")
    for name, proj in [('up_proj', layer_0.mlp.up_proj),
                       ('gate_proj', layer_0.mlp.gate_proj), 
                       ('down_proj', layer_0.mlp.down_proj)]:
        weight = proj.weight.data
        zero_count = (weight == 0).sum().item()
        total_count = weight.numel()
        sparsity = zero_count / total_count * 100
        
        print(f"  {name}: 形状={weight.shape}, 零值={zero_count}/{total_count} ({sparsity:.2f}%)")
        print(f"    权重范围: [{weight.min().item():.6f}, {weight.max().item():.6f}]")
        print(f"    权重均值: {weight.mean().item():.6f}, 标准差: {weight.std().item():.6f}")
        
        # 显示一些具体的权重值
        flat_weights = weight.flatten()
        print(f"    前10个权重值: {flat_weights[:10].tolist()}")
        
        # 检查是否有接近零但不完全为零的值
        near_zero = torch.abs(weight) < 1e-6
        near_zero_count = near_zero.sum().item()
        print(f"    接近零的值 (|w| < 1e-6): {near_zero_count}")

    # 检查整个模型的稀疏性
    print(f"\n=== 整个模型稀疏性分析 ===")
    total_params = 0
    total_zeros = 0
    
    for name, param in model.named_parameters():
        if 'weight' in name:
            param_zeros = (param == 0).sum().item()
            param_total = param.numel()
            total_zeros += param_zeros
            total_params += param_total
            
            if param_zeros > 0:  # 只显示有零值的层
                sparsity = param_zeros / param_total * 100
                print(f"{name}: {param_zeros}/{param_total} ({sparsity:.2f}%) 零值")
    
    overall_sparsity = total_zeros / total_params * 100
    print(f"\n整体稀疏性: {total_zeros}/{total_params} ({overall_sparsity:.2f}%)")

def main():
    parser = argparse.ArgumentParser(description="检查模型稀疏性")
    parser.add_argument("--model_path", type=str, 
                       default="/mnt/sdb/jjji/Pruned_Models/llama2_13b_chat_pruned_models/wanda_50",
                       help="模型路径")
    
    args = parser.parse_args()
    check_model_sparsity(args.model_path)

if __name__ == "__main__":
    main()
