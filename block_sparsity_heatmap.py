import torch
import torch.nn as nn
import matplotlib.pyplot as plt
import numpy as np
from transformers import OPTForCausalLM
import argparse
import os

# Set high quality display
plt.rcParams['font.sans-serif'] = ['DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False
plt.rcParams['figure.dpi'] = 300
plt.rcParams['savefig.dpi'] = 300

def load_pruned_model(model_path):
    """Load pruned OPT model"""
    print(f"Loading model: {model_path}")
    model = OPTForCausalLM.from_pretrained(model_path, torch_dtype=torch.float32)
    model.eval()
    return model

def analyze_layer_weights(model, layer_idx=0):
    """Analyze weights of specified layer"""
    layers = model.model.decoder.layers
    if layer_idx >= len(layers):
        layer_idx = len(layers) - 1
        print(f"Layer index out of range, using last layer: {layer_idx}")
    
    layer = layers[layer_idx]
    print(f"Analyzing layer {layer_idx}")
    
    # Get attention mechanism QKV weights
    attention_weights = {}
    if hasattr(layer.self_attn, 'q_proj'):
        attention_weights['Q'] = layer.self_attn.q_proj.weight.data
    if hasattr(layer.self_attn, 'k_proj'):
        attention_weights['K'] = layer.self_attn.k_proj.weight.data
    if hasattr(layer.self_attn, 'v_proj'):
        attention_weights['V'] = layer.self_attn.v_proj.weight.data
    
    # Get FFN layer weights
    ffn_weights = {}
    if hasattr(layer, 'fc1'):
        ffn_weights['fc1'] = layer.fc1.weight.data
    if hasattr(layer, 'fc2'):
        ffn_weights['fc2'] = layer.fc2.weight.data
    
    return attention_weights, ffn_weights

def block_aggregate(matrix, block_size=16):
    """
    Aggregate matrix into blocks and count non-zero elements in each block
    
    Args:
        matrix: 2D numpy array
        block_size: size of each block (default 16x16)
    
    Returns:
        2D numpy array with block-wise non-zero counts
    """
    h, w = matrix.shape
    
    # Calculate number of blocks
    blocks_h = h // block_size
    blocks_w = w // block_size
    
    # Truncate matrix to fit exact blocks
    matrix_truncated = matrix[:blocks_h * block_size, :blocks_w * block_size]
    
    # Reshape and aggregate
    block_counts = np.zeros((blocks_h, blocks_w))
    
    for i in range(blocks_h):
        for j in range(blocks_w):
            # Extract block
            block = matrix_truncated[i*block_size:(i+1)*block_size, 
                                   j*block_size:(j+1)*block_size]
            # Count non-zero elements
            block_counts[i, j] = np.sum(block != 0)
    
    return block_counts

def plot_block_heatmap(weights_dict, title_prefix, block_size=16, save_path=None):
    """
    Plot block-wise sparsity heatmap
    
    Args:
        weights_dict: dictionary of weight matrices
        title_prefix: prefix for plot titles
        block_size: size of each block
        save_path: path to save the plot
    """
    n_weights = len(weights_dict)
    if n_weights == 0:
        print("No weight data found")
        return
    
    # Calculate figure size
    fig_width = n_weights * 6
    fig_height = 8
    
    fig, axes = plt.subplots(2, n_weights, figsize=(fig_width, fig_height))
    if n_weights == 1:
        axes = axes.reshape(2, 1)
    
    max_block_elements = block_size * block_size
    
    for i, (name, weight) in enumerate(weights_dict.items()):
        weight_np = weight.cpu().numpy()
        
        # Generate block aggregation
        block_counts = block_aggregate(weight_np, block_size)
        
        # Plot 1: Block heatmap
        im1 = axes[0, i].imshow(block_counts, cmap='viridis', aspect='auto', 
                               interpolation='nearest', vmin=0, vmax=max_block_elements)
        
        # Calculate overall statistics
        total_blocks = block_counts.size
        empty_blocks = np.sum(block_counts == 0)
        full_blocks = np.sum(block_counts == max_block_elements)
        avg_nonzero_per_block = np.mean(block_counts)
        
        axes[0, i].set_title(f'{title_prefix} {name} Block Density\n'
                           f'Block Size: {block_size}×{block_size}, Grid: {block_counts.shape[0]}×{block_counts.shape[1]}', 
                           fontsize=12, fontweight='bold')
        axes[0, i].set_xlabel('Block Column Index', fontsize=10)
        axes[0, i].set_ylabel('Block Row Index', fontsize=10)
        
        # Add colorbar
        cbar1 = plt.colorbar(im1, ax=axes[0, i], shrink=0.8)
        cbar1.set_label('Non-zero Elements per Block', rotation=270, labelpad=15)
        
        # Add statistics text
        stats_text = (f'Avg: {avg_nonzero_per_block:.1f}\n'
                     f'Empty: {empty_blocks}\n'
                     f'Full: {full_blocks}\n'
                     f'Total: {total_blocks}')
        axes[0, i].text(0.02, 0.98, stats_text, 
                       transform=axes[0, i].transAxes, 
                       bbox=dict(boxstyle="round,pad=0.3", facecolor="white", alpha=0.8),
                       verticalalignment='top', fontsize=9)
        
        # Plot 2: Distribution of block densities
        axes[1, i].hist(block_counts.flatten(), bins=min(50, max_block_elements+1), 
                       alpha=0.7, color='skyblue', edgecolor='black')
        axes[1, i].set_title(f'{title_prefix} {name} Block Density Distribution', 
                           fontsize=12, fontweight='bold')
        axes[1, i].set_xlabel('Non-zero Elements per Block', fontsize=10)
        axes[1, i].set_ylabel('Number of Blocks', fontsize=10)
        axes[1, i].grid(True, alpha=0.3)
        
        # Add vertical lines for key statistics
        axes[1, i].axvline(avg_nonzero_per_block, color='red', linestyle='--', 
                          label=f'Mean: {avg_nonzero_per_block:.1f}')
        axes[1, i].axvline(max_block_elements/2, color='orange', linestyle='--', 
                          label=f'Half-full: {max_block_elements//2}')
        axes[1, i].legend()
    
    plt.tight_layout()
    
    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight', 
                   facecolor='white', edgecolor='none')
        print(f"Block heatmap saved to: {save_path}")
    
    return fig

def generate_block_analysis(model, layer_idx, block_size=16, output_dir="block_analysis"):
    """Generate block analysis for a specific layer"""
    os.makedirs(output_dir, exist_ok=True)
    
    attention_weights, ffn_weights = analyze_layer_weights(model, layer_idx)
    
    # Generate attention block analysis
    if attention_weights:
        attention_pdf_path = os.path.join(output_dir, f"layer_{layer_idx}_attention_block_{block_size}x{block_size}.pdf")
        fig1 = plot_block_heatmap(attention_weights, f"Layer {layer_idx} Attention", 
                                 block_size, attention_pdf_path)
        plt.close(fig1)
        
        # Also save as PNG for easy viewing
        attention_png_path = os.path.join(output_dir, f"layer_{layer_idx}_attention_block_{block_size}x{block_size}.png")
        fig1_png = plot_block_heatmap(attention_weights, f"Layer {layer_idx} Attention", 
                                     block_size, attention_png_path)
        plt.close(fig1_png)
    
    # Generate FFN block analysis
    if ffn_weights:
        ffn_pdf_path = os.path.join(output_dir, f"layer_{layer_idx}_ffn_block_{block_size}x{block_size}.pdf")
        fig2 = plot_block_heatmap(ffn_weights, f"Layer {layer_idx} FFN", 
                                 block_size, ffn_pdf_path)
        plt.close(fig2)
        
        # Also save as PNG for easy viewing
        ffn_png_path = os.path.join(output_dir, f"layer_{layer_idx}_ffn_block_{block_size}x{block_size}.png")
        fig2_png = plot_block_heatmap(ffn_weights, f"Layer {layer_idx} FFN", 
                                     block_size, ffn_png_path)
        plt.close(fig2_png)

def print_block_statistics(weights_dict, layer_idx, block_size=16):
    """Print detailed block statistics"""
    print(f"\n=== Block Analysis for Layer {layer_idx} (Block Size: {block_size}×{block_size}) ===")
    print("-" * 80)
    
    for name, weight in weights_dict.items():
        weight_np = weight.cpu().numpy()
        block_counts = block_aggregate(weight_np, block_size)
        
        total_elements = weight_np.size
        total_blocks = block_counts.size
        max_block_elements = block_size * block_size
        
        # Overall statistics
        total_nonzero = np.sum(weight_np != 0)
        overall_sparsity = (1 - total_nonzero / total_elements) * 100
        
        # Block statistics
        empty_blocks = np.sum(block_counts == 0)
        full_blocks = np.sum(block_counts == max_block_elements)
        partial_blocks = total_blocks - empty_blocks - full_blocks
        
        avg_nonzero_per_block = np.mean(block_counts)
        std_nonzero_per_block = np.std(block_counts)
        
        print(f"{name} ({weight_np.shape[0]}×{weight_np.shape[1]}):")
        print(f"  Overall Sparsity: {overall_sparsity:.2f}%")
        print(f"  Total Blocks: {total_blocks} ({block_counts.shape[0]}×{block_counts.shape[1]})")
        print(f"  Empty Blocks: {empty_blocks} ({empty_blocks/total_blocks*100:.1f}%)")
        print(f"  Full Blocks: {full_blocks} ({full_blocks/total_blocks*100:.1f}%)")
        print(f"  Partial Blocks: {partial_blocks} ({partial_blocks/total_blocks*100:.1f}%)")
        print(f"  Avg Non-zero per Block: {avg_nonzero_per_block:.2f} ± {std_nonzero_per_block:.2f}")
        print(f"  Block Density Range: [{block_counts.min():.0f}, {block_counts.max():.0f}]")
        print()

def main():
    parser = argparse.ArgumentParser(description="Generate block-wise sparsity heatmaps")
    parser.add_argument("--model_path", type=str, 
                       default="/mnt/sdb/llm_models/opt-125m-pruned2.4",
                       help="Path to the pruned model")
    parser.add_argument("--layer", type=int, default=0,
                       help="Layer to analyze (0-indexed)")
    parser.add_argument("--block_size", type=int, default=16,
                       help="Block size for aggregation (default: 16)")
    parser.add_argument("--output_dir", type=str, default="block_analysis",
                       help="Output directory for analysis files")
    
    args = parser.parse_args()
    
    # Load model
    model = load_pruned_model(args.model_path)
    
    # Display model information
    print(f"\nModel Information:")
    print(f"Number of layers: {len(model.model.decoder.layers)}")
    print(f"Hidden dimension: {model.config.hidden_size}")
    print(f"FFN intermediate dimension: {model.config.ffn_dim}")
    print(f"Number of attention heads: {model.config.num_attention_heads}")
    
    # Analyze specified layer
    attention_weights, ffn_weights = analyze_layer_weights(model, args.layer)
    
    # Print detailed statistics
    print_block_statistics(attention_weights, args.layer, args.block_size)
    print_block_statistics(ffn_weights, args.layer, args.block_size)
    
    # Generate visualizations
    generate_block_analysis(model, args.layer, args.block_size, args.output_dir)
    
    print(f"\nBlock analysis files saved in directory: {args.output_dir}")

if __name__ == "__main__":
    main()
