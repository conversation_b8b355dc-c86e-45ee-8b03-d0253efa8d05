#!/usr/bin/env python3
"""
块级稀疏性可视化脚本
按照指定块大小（如16x16, 64x64）进行对齐采样，可视化块内部的NNZ分布
"""

import torch
import torch.nn as nn
import matplotlib.pyplot as plt
import numpy as np
from transformers import OPTForCausalLM, LlamaForCausalLM, AutoConfig
from matplotlib.backends.backend_pdf import PdfPages
import argparse
import os
import random
import sys
from datetime import datetime

# 设置高质量显示
plt.rcParams['font.sans-serif'] = ['DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False
plt.rcParams['figure.dpi'] = 300
plt.rcParams['savefig.dpi'] = 300

class Logger:
    """Logger class to output to both console and file"""
    def __init__(self, log_file_path):
        self.log_file = open(log_file_path, 'w')
        self.console = sys.stdout

    def write(self, message):
        self.console.write(message)
        self.log_file.write(message)
        self.log_file.flush()

    def flush(self):
        self.console.flush()
        self.log_file.flush()

    def close(self):
        self.log_file.close()

def load_pruned_model(model_path):
    """Load pruned model (auto-detect OPT or LLaMA)"""
    print(f"Loading model: {model_path}")

    try:
        config = AutoConfig.from_pretrained(model_path)
        model_type = config.model_type.lower()
        print(f"Detected model type: {model_type}")

        if model_type == 'opt':
            model = OPTForCausalLM.from_pretrained(model_path, torch_dtype=torch.float32)
        elif model_type == 'llama':
            model = LlamaForCausalLM.from_pretrained(model_path, torch_dtype=torch.float32)
        else:
            print(f"Unsupported model type: {model_type}, trying LLaMA as default")
            model = LlamaForCausalLM.from_pretrained(model_path, torch_dtype=torch.float32)
    except Exception as e:
        print(f"Error loading model: {e}")
        print("Trying LLaMA as default...")
        model = LlamaForCausalLM.from_pretrained(model_path, torch_dtype=torch.float32)

    model.eval()
    return model

def get_layer_weights(model, layer_idx=0):
    """Get weights of specified layer (supports OPT and LLaMA)"""
    # Determine model architecture
    if hasattr(model.model, 'decoder'):
        # OPT model
        layers = model.model.decoder.layers
        model_type = 'opt'
    else:
        # LLaMA model
        layers = model.model.layers
        model_type = 'llama'

    if layer_idx >= len(layers):
        layer_idx = len(layers) - 1
        print(f"Layer index out of range, using last layer: {layer_idx}")

    layer = layers[layer_idx]
    print(f"Analyzing layer {layer_idx} (model type: {model_type})")

    weights_dict = {}

    # Get attention mechanism weights
    if hasattr(layer.self_attn, 'q_proj'):
        weights_dict['Q_proj'] = layer.self_attn.q_proj.weight.data
    if hasattr(layer.self_attn, 'k_proj'):
        weights_dict['K_proj'] = layer.self_attn.k_proj.weight.data
    if hasattr(layer.self_attn, 'v_proj'):
        weights_dict['V_proj'] = layer.self_attn.v_proj.weight.data
    if hasattr(layer.self_attn, 'o_proj'):
        weights_dict['O_proj'] = layer.self_attn.o_proj.weight.data

    # Get FFN/MLP weights
    if model_type == 'opt':
        if hasattr(layer, 'fc1'):
            weights_dict['fc1'] = layer.fc1.weight.data
        if hasattr(layer, 'fc2'):
            weights_dict['fc2'] = layer.fc2.weight.data
    else:
        # LLaMA model
        if hasattr(layer, 'mlp'):
            if hasattr(layer.mlp, 'up_proj'):
                weights_dict['up_proj'] = layer.mlp.up_proj.weight.data
            if hasattr(layer.mlp, 'gate_proj'):
                weights_dict['gate_proj'] = layer.mlp.gate_proj.weight.data
            if hasattr(layer.mlp, 'down_proj'):
                weights_dict['down_proj'] = layer.mlp.down_proj.weight.data

    return weights_dict

def extract_aligned_blocks(weight_matrix, block_size, sample_ratio=0.2):
    """
    从权重矩阵中提取对齐的块
    
    Args:
        weight_matrix: 权重矩阵 (torch.Tensor)
        block_size: 块大小 (int)，如16表示16x16块
        sample_ratio: 采样比例 (float)，如0.2表示采样20%的块
    
    Returns:
        blocks: 采样的块列表
        block_positions: 块的位置信息
    """
    weight_np = weight_matrix.cpu().numpy()
    rows, cols = weight_np.shape
    
    # 计算可以完整划分的块数量
    num_blocks_row = rows // block_size
    num_blocks_col = cols // block_size
    
    print(f"Weight matrix shape: {weight_np.shape}")
    print(f"Block size: {block_size}x{block_size}")
    print(f"Number of blocks: {num_blocks_row} x {num_blocks_col} = {num_blocks_row * num_blocks_col}")
    
    # 生成所有可能的块位置
    all_block_positions = []
    for i in range(num_blocks_row):
        for j in range(num_blocks_col):
            all_block_positions.append((i, j))
    
    # 随机采样指定比例的块
    num_sample_blocks = max(1, int(len(all_block_positions) * sample_ratio))
    sampled_positions = random.sample(all_block_positions, num_sample_blocks)
    
    print(f"Sampled blocks: {num_sample_blocks}")
    
    # 提取采样的块
    blocks = []
    block_info = []
    
    for i, (block_row, block_col) in enumerate(sampled_positions):
        start_row = block_row * block_size
        end_row = start_row + block_size
        start_col = block_col * block_size
        end_col = start_col + block_size
        
        block = weight_np[start_row:end_row, start_col:end_col]
        blocks.append(block)
        
        # 计算块的稀疏性信息
        nnz = np.count_nonzero(block)
        total = block.size
        sparsity = (total - nnz) / total * 100
        
        block_info.append({
            'index': i,
            'position': (block_row, block_col),
            'global_position': (start_row, start_col),
            'nnz': nnz,
            'total': total,
            'sparsity': sparsity,
            'density': nnz / total * 100
        })
    
    return blocks, block_info

def create_block_sparsity_plot(blocks, block_info, weight_name, block_size, save_path=None):
    """
    Create block sparsity pattern plot similar to original script style

    Args:
        blocks: List of blocks
        block_info: Block information list
        weight_name: Weight name
        block_size: Block size
        save_path: Save path
    """
    num_blocks = len(blocks)
    if num_blocks == 0:
        print("No blocks to visualize")
        return

    # Calculate layout - similar to original script
    cols = min(10, num_blocks)  # Max 10 blocks per row for better visibility
    rows = (num_blocks + cols - 1) // cols

    # Calculate figure size based on number of blocks
    fig_width = cols * 2.5
    fig_height = rows * 2.5

    fig, axes = plt.subplots(rows, cols, figsize=(fig_width, fig_height))

    # Handle different subplot configurations
    if rows == 1 and cols == 1:
        axes = [axes]
    elif rows == 1:
        axes = axes
    else:
        axes = axes.flatten()

    # Overall sparsity calculation
    total_nnz = sum(info['nnz'] for info in block_info)
    total_elements = sum(info['total'] for info in block_info)
    overall_sparsity = (total_elements - total_nnz) / total_elements * 100

    fig.suptitle(f'{weight_name} Block Sparsity Pattern\n'
                 f'Block Size: {block_size}x{block_size}, Sampled Blocks: {num_blocks}, '
                 f'Overall Sparsity: {overall_sparsity:.1f}%',
                 fontsize=12, fontweight='bold')

    for i in range(num_blocks):
        ax = axes[i] if num_blocks > 1 else axes[0]

        block = blocks[i]
        info = block_info[i]

        # Create binary pattern: 1 for non-zero, 0 for zero (similar to original)
        binary_pattern = (block != 0).astype(float)

        # Plot with custom colormap: white for 0, colored for 1
        ax.imshow(binary_pattern, cmap='Blues', aspect='auto',
                 interpolation='nearest', vmin=0, vmax=1)

        # Calculate sparsity for this block
        block_sparsity = (block == 0).mean() * 100

        ax.set_title(f'Block {i+1}\nPos: ({info["position"][0]}, {info["position"][1]})\n'
                    f'Sparsity: {block_sparsity:.1f}%',
                    fontsize=9)

        # Set labels
        ax.set_xlabel('Input Dim', fontsize=8)
        ax.set_ylabel('Output Dim', fontsize=8)

        # Add text annotation for non-zero count
        ax.text(0.02, 0.98, f'NNZ: {info["nnz"]}\nTotal: {info["total"]}',
                transform=ax.transAxes,
                bbox=dict(boxstyle="round,pad=0.3", facecolor="white", alpha=0.8),
                verticalalignment='top', fontsize=8)

        # Set ticks
        if block_size > 8:
            tick_positions = np.linspace(0, block_size-1, 5).astype(int)
            ax.set_xticks(tick_positions)
            ax.set_yticks(tick_positions)
            ax.set_xticklabels(tick_positions, fontsize=7)
            ax.set_yticklabels(tick_positions, fontsize=7)
        else:
            ax.set_xticks(range(block_size))
            ax.set_yticks(range(block_size))

    # Hide extra subplots
    for i in range(num_blocks, len(axes)):
        axes[i].set_visible(False)

    plt.tight_layout()

    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight',
                   facecolor='white', edgecolor='none')
        print(f"Block sparsity pattern saved to: {save_path}")

    return fig

def create_sparsity_distribution_plot(block_info, weight_name, block_size, save_path=None):
    """
    Create sparsity distribution histogram

    Args:
        block_info: Block information list
        weight_name: Weight name
        block_size: Block size
        save_path: Save path
    """
    sparsities = [info['sparsity'] for info in block_info]

    fig, ax = plt.subplots(1, 1, figsize=(10, 6))

    # Create histogram with 20 bins from 0 to 100
    bins = np.linspace(0, 100, 21)  # 20 bins
    counts, bin_edges, _ = ax.hist(sparsities, bins=bins, alpha=0.7, edgecolor='black', color='skyblue')

    # Calculate percentages
    total_blocks = len(sparsities)
    percentages = (counts / total_blocks) * 100

    # Set labels and title
    ax.set_xlabel('Sparsity (%)', fontsize=12)
    ax.set_ylabel('Number of Blocks', fontsize=12)
    ax.set_title(f'{weight_name} Sparsity Distribution\n'
                 f'Block Size: {block_size}x{block_size}, Total Blocks: {total_blocks}',
                 fontsize=14, fontweight='bold')

    # Add percentage labels on top of bars
    for i, (count, percentage) in enumerate(zip(counts, percentages)):
        if count > 0:
            bin_center = (bin_edges[i] + bin_edges[i+1]) / 2
            ax.text(bin_center, count + total_blocks * 0.01, f'{percentage:.1f}%',
                   ha='center', va='bottom', fontsize=9, rotation=0)

    # Add grid
    ax.grid(True, alpha=0.3)

    # Add statistics text
    stats_text = f'Mean: {np.mean(sparsities):.2f}%\n'
    stats_text += f'Std: {np.std(sparsities):.2f}%\n'
    stats_text += f'Min: {np.min(sparsities):.2f}%\n'
    stats_text += f'Max: {np.max(sparsities):.2f}%'

    ax.text(0.02, 0.98, stats_text, transform=ax.transAxes,
            bbox=dict(boxstyle="round,pad=0.5", facecolor="white", alpha=0.8),
            verticalalignment='top', fontsize=10)

    plt.tight_layout()

    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight',
                   facecolor='white', edgecolor='none')
        print(f"Sparsity distribution saved to: {save_path}")

    return fig



def process_layer_blocks(model, layer_idx, block_size, sample_ratio, output_dir):
    """
    Process block-level sparsity analysis for specified layer

    Args:
        model: Model
        layer_idx: Layer index
        block_size: Block size
        sample_ratio: Sampling ratio
        output_dir: Output directory
    """
    print(f"\n=== Processing Layer {layer_idx} ===")

    # Get weights
    weights_dict = get_layer_weights(model, layer_idx)

    if not weights_dict:
        print("No weight data found")
        return

    # Extract blocks for each weight matrix
    for weight_name, weight_matrix in weights_dict.items():
        print(f"\nProcessing weight: {weight_name}")

        # Extract aligned blocks
        blocks, block_info = extract_aligned_blocks(weight_matrix, block_size, sample_ratio)

        if not blocks:
            print(f"Weight {weight_name} cannot extract blocks (matrix too small)")
            continue

        # Visualize blocks
        block_viz_path = os.path.join(output_dir,
                                     f"layer_{layer_idx}_{weight_name}_block_sparsity_{block_size}x{block_size}.pdf")
        fig = create_block_sparsity_plot(blocks, block_info, weight_name, block_size, block_viz_path)
        plt.close(fig)

        # Create sparsity distribution plot
        dist_viz_path = os.path.join(output_dir,
                                    f"layer_{layer_idx}_{weight_name}_sparsity_distribution_{block_size}x{block_size}.pdf")
        fig = create_sparsity_distribution_plot(block_info, weight_name, block_size, dist_viz_path)
        plt.close(fig)

        # Print statistics
        sparsities = [info['sparsity'] for info in block_info]
        print(f"  Sampled blocks: {len(blocks)}")
        print(f"  Average sparsity: {np.mean(sparsities):.2f}%")
        print(f"  Sparsity range: [{np.min(sparsities):.2f}%, {np.max(sparsities):.2f}%]")

def main():
    parser = argparse.ArgumentParser(description="Block-level sparsity visualization")
    parser.add_argument("--model_path", type=str,
                       default="/mnt/sdb/jjji/Pruned_Models/llama2_13b_chat_pruned_models/wanda_50",
                       help="Path to the pruned model")
    parser.add_argument("--layer", type=int, default=8,
                       help="Layer index to analyze (default: 0)")
    parser.add_argument("--block_size", type=int, default=16,
                       help="Block size (default: 16, means 16x16 blocks)")
    parser.add_argument("--sample_ratio", type=float, default=0.005,
                       help="Sampling ratio (default: 0.2, means sample 20% of blocks)")
    parser.add_argument("--output_dir", type=str, default="block_sparsity_patterns_50_16*16_llama2_8layer",
                       help="Output directory")
    parser.add_argument("--seed", type=int, default=42,
                       help="Random seed")

    args = parser.parse_args()

    # Set random seed
    random.seed(args.seed)
    np.random.seed(args.seed)

    # Create output directory
    os.makedirs(args.output_dir, exist_ok=True)

    # Setup logging
    log_file_path = os.path.join(args.output_dir, f"analysis_log_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt")
    logger = Logger(log_file_path)
    sys.stdout = logger

    print(f"Analysis started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"Log file: {log_file_path}")
    print("="*80)

    # Load model
    model = load_pruned_model(args.model_path)

    # Display model information
    print(f"\nModel Information:")
    if hasattr(model.model, 'decoder'):
        print(f"Number of layers: {len(model.model.decoder.layers)}")
    else:
        print(f"Number of layers: {len(model.model.layers)}")
    print(f"Hidden dimension: {model.config.hidden_size}")
    print(f"Number of attention heads: {model.config.num_attention_heads}")

    print(f"\nAnalysis Parameters:")
    print(f"Block size: {args.block_size}x{args.block_size}")
    print(f"Sample ratio: {args.sample_ratio * 100:.1f}%")
    print(f"Random seed: {args.seed}")

    # Process specified layer
    process_layer_blocks(model, args.layer, args.block_size, args.sample_ratio, args.output_dir)

    print(f"\nAnalysis complete! Results saved in: {args.output_dir}")
    print(f"Analysis finished at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

    # Close logger
    sys.stdout = logger.console
    logger.close()

if __name__ == "__main__":
    main()
