Analysis started at: 2025-07-31 20:05:04
Log file: block_sparsity_patterns_50_16*16_llama2_8layer/analysis_log_20250731_200504.txt
================================================================================
Loading model: /mnt/sdb/jjji/Pruned_Models/llama2_13b_chat_pruned_models/wanda_50
Detected model type: llama

Model Information:
Number of layers: 40
Hidden dimension: 5120
Number of attention heads: 40

Analysis Parameters:
Block size: 16x16
Sample ratio: 0.5%
Random seed: 42

=== Processing Layer 8 ===
Analyzing layer 8 (model type: llama)

Processing weight: Q_proj
Weight matrix shape: (5120, 5120)
Block size: 16x16
Number of blocks: 320 x 320 = 102400
Sampled blocks: 512
Block sparsity pattern saved to: block_sparsity_patterns_50_16*16_llama2_8layer/layer_8_Q_proj_block_sparsity_16x16.pdf
Sparsity distribution saved to: block_sparsity_patterns_50_16*16_llama2_8layer/layer_8_Q_proj_sparsity_distribution_16x16.pdf
  Sampled blocks: 512
  Average sparsity: 49.88%
  Sparsity range: [40.23%, 57.81%]

Processing weight: K_proj
Weight matrix shape: (5120, 5120)
Block size: 16x16
Number of blocks: 320 x 320 = 102400
Sampled blocks: 512
Block sparsity pattern saved to: block_sparsity_patterns_50_16*16_llama2_8layer/layer_8_K_proj_block_sparsity_16x16.pdf
Sparsity distribution saved to: block_sparsity_patterns_50_16*16_llama2_8layer/layer_8_K_proj_sparsity_distribution_16x16.pdf
  Sampled blocks: 512
  Average sparsity: 49.93%
  Sparsity range: [38.28%, 58.98%]

Processing weight: V_proj
Weight matrix shape: (5120, 5120)
Block size: 16x16
Number of blocks: 320 x 320 = 102400
Sampled blocks: 512
Block sparsity pattern saved to: block_sparsity_patterns_50_16*16_llama2_8layer/layer_8_V_proj_block_sparsity_16x16.pdf
Sparsity distribution saved to: block_sparsity_patterns_50_16*16_llama2_8layer/layer_8_V_proj_sparsity_distribution_16x16.pdf
  Sampled blocks: 512
  Average sparsity: 50.13%
  Sparsity range: [41.41%, 59.38%]

Processing weight: O_proj
Weight matrix shape: (5120, 5120)
Block size: 16x16
Number of blocks: 320 x 320 = 102400
Sampled blocks: 512
Block sparsity pattern saved to: block_sparsity_patterns_50_16*16_llama2_8layer/layer_8_O_proj_block_sparsity_16x16.pdf
Sparsity distribution saved to: block_sparsity_patterns_50_16*16_llama2_8layer/layer_8_O_proj_sparsity_distribution_16x16.pdf
  Sampled blocks: 512
  Average sparsity: 50.71%
  Sparsity range: [28.12%, 98.05%]

Processing weight: up_proj
Weight matrix shape: (13824, 5120)
Block size: 16x16
Number of blocks: 864 x 320 = 276480
Sampled blocks: 1382
Block sparsity pattern saved to: block_sparsity_patterns_50_16*16_llama2_8layer/layer_8_up_proj_block_sparsity_16x16.pdf
Sparsity distribution saved to: block_sparsity_patterns_50_16*16_llama2_8layer/layer_8_up_proj_sparsity_distribution_16x16.pdf
  Sampled blocks: 1382
  Average sparsity: 50.19%
  Sparsity range: [40.62%, 60.55%]

Processing weight: gate_proj
Weight matrix shape: (13824, 5120)
Block size: 16x16
Number of blocks: 864 x 320 = 276480
Sampled blocks: 1382
Block sparsity pattern saved to: block_sparsity_patterns_50_16*16_llama2_8layer/layer_8_gate_proj_block_sparsity_16x16.pdf
Sparsity distribution saved to: block_sparsity_patterns_50_16*16_llama2_8layer/layer_8_gate_proj_sparsity_distribution_16x16.pdf
  Sampled blocks: 1382
  Average sparsity: 50.00%
  Sparsity range: [39.45%, 59.77%]

Processing weight: down_proj
Weight matrix shape: (5120, 13824)
Block size: 16x16
Number of blocks: 320 x 864 = 276480
Sampled blocks: 1382
Block sparsity pattern saved to: block_sparsity_patterns_50_16*16_llama2_8layer/layer_8_down_proj_block_sparsity_16x16.pdf
Sparsity distribution saved to: block_sparsity_patterns_50_16*16_llama2_8layer/layer_8_down_proj_sparsity_distribution_16x16.pdf
  Sampled blocks: 1382
  Average sparsity: 49.92%
  Sparsity range: [39.06%, 62.11%]

Analysis complete! Results saved in: block_sparsity_patterns_50_16*16_llama2_8layer
Analysis finished at: 2025-07-31 20:20:58
